import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  <PERSON>vider,
  Grid2,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  SelectChangeEvent,
  Tooltip,
  Typography,
} from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import { add, addYears, endOfMonth, endOfYear, format, startOfMonth, startOfYear, subYears } from "date-fns";
import React, { Key, useCallback, useEffect, useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { LeaveAttribute, LeavePolicyDetails } from "src/services/api_definitions/leaveManagement.service";
import leaveManagementService from "src/services/leaveManagement.service";
import masterdataService from "src/services/masterdata.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { isSubset } from "src/utils/dataUtils";
import AddEditLeavePolicyListItemTable from "./AddEditListItemTable";
import { LeavePoliciyModes, LeavePolicyActionModes } from "./LeavePolicies";

interface AddEditLeavePoliciesProps {
  actionMode: LeavePolicyActionModes;
  setCurrentViewMode: (currentViewMode: LeavePoliciyModes) => void;
  selectedRow: LeavePolicyDetails | null;
  setSelectedRow: (selectedRow: LeavePolicyDetails | null) => void;
}

type DefaultFormState = {
  employeeType: string[];
  name: string;
  startDate: string;
  endDate: string;
};

const translations = {
  name: "Leave Policy Name",
  employeeType: "Employee Type",
  startDate: "Start Month",
  endDate: "End Month",
  addLeavePolicy: "Add Leave Policy",
  editLeavePolicy: "Edit Leave Policy",
  duplicateLeavePolicy: "Duplicate Leave Policy",
};

const areAccrualDetailsValid = (attribute: LeaveAttribute) => {
  if (!attribute?.allocationMethod) {
    return false;
  }
  if (attribute?.allocationMethod === "Pro-Rated Accrued") {
    return !!attribute.accrualFrequency && attribute?.days && Number(attribute?.days) !== 0;
  }
  return !!attribute?.days && Number(attribute?.days) !== 0;
};

const areCarryForwardDetailsValid = (attribute: LeaveAttribute) =>
  attribute.carryForwardAllowed ? attribute.carryForwardLimit && Number(attribute.carryForwardLimit) !== 0 : true;

const isAutoApproveValid = (attribute: LeaveAttribute) =>
  attribute.autoApproveDays == null ||
  (Number(attribute.autoApproveDays) >= 1 && Number(attribute.autoApproveDays) <= 5);

const areAnyFieldsDirty = (selectedAttribute: LeaveAttribute, attribute: LeaveAttribute) =>
  selectedAttribute.enabled !== attribute?.enabled ||
  Number(selectedAttribute.carryForwardLimit) !== Number(attribute?.carryForwardLimit) ||
  Number(selectedAttribute.days) !== Number(attribute?.days) ||
  Number(selectedAttribute.maximumAllowed) !== Number(attribute?.maximumAllowed) ||
  Number(selectedAttribute.minimumAllowed) !== Number(attribute?.minimumAllowed) ||
  (selectedAttribute?.allocationMethod === "Pro-Rated Accrued"
    ? selectedAttribute.accrualFrequency !== attribute?.accrualFrequency
    : false) ||
  selectedAttribute.oneTime !== attribute?.oneTime ||
  selectedAttribute.carryForwardAllowed !== attribute?.carryForwardAllowed ||
  selectedAttribute.allocationMethod !== attribute?.allocationMethod ||
  selectedAttribute.halfDayAllowed !== attribute?.halfDayAllowed ||
  selectedAttribute.weekendIncluded !== attribute?.weekendIncluded ||
  selectedAttribute.holidayIncluded !== attribute?.holidayIncluded ||
  selectedAttribute.allowedOnNotice !== attribute?.allowedOnNotice ||
  selectedAttribute.allowedOnProbation !== attribute?.allowedOnProbation ||
  Number(selectedAttribute.autoApproveDays ?? null) !== Number(attribute?.autoApproveDays ?? null);

const hasValidChangesInRow = (selectedRow: LeavePolicyDetails, selectedAttribute: LeaveAttribute) => {
  const attribute = selectedRow.leaveAttributes.find((attr) => attr.leaveType === selectedAttribute.leaveType);
  if (!attribute) {
    return true;
  }
  return areAnyFieldsDirty(selectedAttribute, attribute);
};

const areSomeLeavesEnabled = (leaveAttributes: LeaveAttribute[]) =>
  leaveAttributes.some((attribute) => attribute.enabled);

const AddEditLeavePolicies: React.FC<AddEditLeavePoliciesProps> = ({
  actionMode,
  selectedRow,
  setCurrentViewMode,
  setSelectedRow,
}) => {
  const isEdit = actionMode === "edit";
  const dispatch = useAppDispatch();
  const [leaveFormState, setLeaveFormState] = useState<LeaveAttribute[]>([]);

  const getDefaultLeaveTypeFormState = useCallback(
    (leaveTypes: string[] = []): LeaveAttribute[] => {
      return (
        (leaveTypes || [])?.map((leaveType) => {
          const selectedRowDetail = selectedRow?.leaveAttributes?.find((leaveAttribute) =>
            leaveType.includes(leaveAttribute?.leaveType as string),
          );
          return {
            carryForwardLimit: selectedRowDetail?.carryForwardLimit || null,
            carryForwardAllowed: selectedRowDetail?.carryForwardAllowed || false,
            days: selectedRowDetail?.days || null,
            enabled: selectedRowDetail?.enabled || false,
            halfDayAllowed: selectedRowDetail?.halfDayAllowed || false,
            holidayIncluded: selectedRowDetail?.holidayIncluded || false,
            leaveType,
            maximumAllowed: selectedRowDetail?.maximumAllowed || null,
            minimumAllowed: selectedRowDetail?.minimumAllowed || null,
            oneTime: selectedRowDetail?.oneTime || false,
            accrualFrequency: selectedRowDetail?.accrualFrequency || null,
            weekendIncluded: selectedRowDetail?.weekendIncluded || false,
            allowedOnProbation: selectedRowDetail?.allowedOnProbation || false,
            allowedOnNotice: selectedRowDetail?.allowedOnNotice || false,
            allocationMethod: selectedRowDetail?.allocationMethod || null,
            autoApproveDays: selectedRowDetail?.autoApproveDays ?? null,
          };
        }) || []
      );
    },
    [selectedRow],
  );

  const [employeeTypes, leaveTypes, proRataFrequencies, allocationMethods] = useQueries({
    queries: [
      {
        queryKey: ["get-employee-types"],
        queryFn: async () => masterdataService.getACLs("EmployeeType"),
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-leave-type-details"],
        queryFn: async () => {
          const resp = await leaveManagementService.getLeaveDetails();
          const defaultLeaves = resp?.default_leaves || [];
          const customLeaves = resp?.custom_leaves || [];
          return [...defaultLeaves.map((leave) => leave.type), ...customLeaves.map((leave) => leave?.type)];
        },
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-frequency-types"],
        queryFn: async () => masterdataService.getACLs<string>("FrequencyType"),
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-allocation-methods"],
        queryFn: async () => masterdataService.getACLs<string>("LeaveAllocationMethod"),
        refetchOnWindowFocus: false,
      },
    ],
  });

  const createLeavePolicy = useMutation({
    mutationKey: ["create-leave-policy"],
    mutationFn: () =>
      leaveManagementService.createLeavePolicies({
        employee_types: typedFormDetails?.employeeType,
        name: typedFormDetails?.name,
        start_date: format(typedFormDetails?.startDate, "yyyy-MM-dd"),
        end_date: format(typedFormDetails?.endDate, "yyyy-MM-dd"),
        leave_attributes: leaveFormState
          ?.filter((leaveAttribute) => leaveAttribute?.enabled)
          ?.map((leaveAttribute) => ({
            carry_forward_limit: leaveAttribute?.carryForwardLimit,
            half_day_allowed: leaveAttribute?.halfDayAllowed,
            holiday_included: leaveAttribute?.holidayIncluded,
            leave_type: leaveAttribute?.leaveType,
            maximum_allowed: leaveAttribute?.maximumAllowed,
            minimum_allowed: leaveAttribute?.minimumAllowed,
            one_time: leaveAttribute?.oneTime,
            accrual_frequency: leaveAttribute?.accrualFrequency,
            weekend_included: leaveAttribute?.weekendIncluded,
            enabled: leaveAttribute?.enabled,
            carry_forward_allowed: leaveAttribute?.carryForwardAllowed,
            days: leaveAttribute?.days,
            allowed_on_probation: leaveAttribute?.allowedOnProbation,
            allowed_on_notice: leaveAttribute?.allowedOnNotice,
            allocation_method: leaveAttribute?.allocationMethod,
            auto_approve_days: leaveAttribute?.autoApproveDays ?? null,
          })),
      }),
    onSuccess: () => {
      dispatch(setFullviewMode(false));
      setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
      setLeaveFormState([]);
    },
  });

  const updateLeavePolicy = useMutation({
    mutationKey: ["update-leave-policy"],
    mutationFn: () =>
      leaveManagementService.updateLeavePolicies({
        employee_types: typedFormDetails?.employeeType,
        name: selectedRow?.name as string,
        start_date: format(typedFormDetails?.startDate, "yyyy-MM-dd"),
        end_date: format(typedFormDetails?.endDate, "yyyy-MM-dd"),
        leave_attributes: leaveFormState?.map((leaveAttribute) => ({
          carry_forward_limit: leaveAttribute?.carryForwardLimit,
          half_day_allowed: leaveAttribute?.halfDayAllowed,
          holiday_included: leaveAttribute?.holidayIncluded,
          leave_type: leaveAttribute?.leaveType,
          maximum_allowed: leaveAttribute?.maximumAllowed,
          minimum_allowed: leaveAttribute?.minimumAllowed,
          one_time: leaveAttribute?.oneTime,
          accrual_frequency: leaveAttribute?.accrualFrequency,
          weekend_included: leaveAttribute?.weekendIncluded,
          enabled: leaveAttribute?.enabled,
          carry_forward_allowed: leaveAttribute?.carryForwardAllowed,
          days: leaveAttribute?.days,
          allowed_on_probation: leaveAttribute?.allowedOnProbation,
          allowed_on_notice: leaveAttribute?.allowedOnNotice,
          allocation_method: leaveAttribute?.allocationMethod,
          auto_approve_days: leaveAttribute?.autoApproveDays ?? null,
        })),
        new_name: typedFormDetails?.name,
      }),
    onSuccess: () => {
      dispatch(setFullviewMode(false));
      setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
      setLeaveFormState([]);
      setSelectedRow(null);
    },
  });

  const defaultFormState: DefaultFormState = useMemo(() => {
    const isDuplicate = actionMode === "duplicate";
    return {
      name: isDuplicate ? "" : selectedRow?.name || "",
      employeeType: isDuplicate ? [] : selectedRow?.employeeTypes || [],
      startDate: isDuplicate ? "" : selectedRow?.startDate || "",
      endDate: isDuplicate ? "" : selectedRow?.endDate || "",
    };
  }, [selectedRow, actionMode]);

  const { formDetails, formErrors, handleChange, setFormDetail } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      employeeType: [],
      endDate: [],
      startDate: [],
      name: [],
    },
  });

  useEffect(() => {
    const leaveState = getDefaultLeaveTypeFormState(leaveTypes?.data || []);
    setLeaveFormState(leaveState.sort((a, b) => (a?.enabled === b?.enabled ? 0 : a?.enabled ? -1 : 1)));
  }, [leaveTypes?.data]);

  const typedFormDetails = formDetails as DefaultFormState;
  const typedFormErrors = formErrors as Record<keyof DefaultFormState, string>;

  const onBackClick = () => {
    dispatch(setFullviewMode(false));
    setSelectedRow(null);
    setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
  };

  const handleCheckboxChange = (ev: SelectChangeEvent<string[]>) => {
    const { value } = ev.target;
    setFormDetail("employeeType", value);
  };

  const handleFormChange = (key: string, value: unknown, index: number) => {
    if (key === "carryForwardAllowed") {
      setLeaveFormState((prevState) => {
        const updatedLeaveTypes = [...prevState];
        updatedLeaveTypes[index] = {
          ...updatedLeaveTypes[index],
          [key]: value as boolean,
          carryForwardLimit: value ? updatedLeaveTypes[index].carryForwardLimit : ("" as unknown as number),
        };
        return updatedLeaveTypes;
      });
      return;
    }

    if (key === "allocationMethod") {
      setLeaveFormState((prevState) => {
        const updatedLeaveTypes = [...prevState];
        updatedLeaveTypes[index] = {
          ...updatedLeaveTypes[index],
          [key]: value as string,
          accrualFrequency: null,
        };
        return updatedLeaveTypes;
      });
      return;
    }

    setLeaveFormState((prevState) => {
      const updatedLeaveTypes = [...prevState];
      updatedLeaveTypes[index] = {
        ...updatedLeaveTypes[index],
        [key]: value,
      };
      return updatedLeaveTypes;
    });
  };

  const isCreateEnabled = useCallback((): boolean => {
    if (!areSomeLeavesEnabled(leaveFormState)) {
      return false;
    }
    const hasBasicDependentValidationsPassed = leaveFormState.every((attribute) => {
      if (!attribute.enabled) return true;
      return (
        areCarryForwardDetailsValid(attribute) && areAccrualDetailsValid(attribute) && isAutoApproveValid(attribute)
      );
    });

    return (
      typedFormDetails.employeeType.length !== 0 &&
      !!typedFormDetails.endDate &&
      !!typedFormDetails.startDate &&
      !!typedFormDetails.name &&
      hasBasicDependentValidationsPassed
    );
  }, [leaveFormState, typedFormDetails]);

  const isUpdateEnabled = useCallback(
    (selectedLeavePolicyDetails: LeavePolicyDetails) => {
      if (!areSomeLeavesEnabled(leaveFormState)) {
        return false;
      }

      const haveBasicFormDetailsChanged =
        (typedFormDetails?.name != null && selectedLeavePolicyDetails?.name !== typedFormDetails?.name) ||
        (typedFormDetails.employeeType?.some(
          (employeeType) => !selectedLeavePolicyDetails.employeeTypes?.includes(employeeType),
        ) &&
          typedFormDetails?.employeeType?.length !== 0) ||
        (typedFormDetails?.startDate !== null &&
          selectedLeavePolicyDetails?.startDate !== typedFormDetails?.startDate) ||
        (typedFormDetails?.endDate !== null && selectedLeavePolicyDetails?.endDate !== typedFormDetails?.endDate);

      if (haveBasicFormDetailsChanged) {
        return true;
      }

      const enabledLeaveAttributes = leaveFormState?.filter((eachLeaveFromState) => eachLeaveFromState.enabled);
      const enabledAttributeKeys = enabledLeaveAttributes?.map((eachAttribute) => eachAttribute?.leaveType || "") || [];
      const previouslySelectedKeys =
        selectedLeavePolicyDetails?.leaveAttributes?.map((eachAttribute) => eachAttribute?.leaveType || "") || [];
      const hasAnyLeaveTypeBeenDisabled = !isSubset(previouslySelectedKeys, enabledAttributeKeys);

      if (hasAnyLeaveTypeBeenDisabled) {
        return true;
      }

      const hasBasicDependentValidationsPassed = enabledLeaveAttributes?.every(
        (selectedAttribute) =>
          areAccrualDetailsValid(selectedAttribute) && areCarryForwardDetailsValid(selectedAttribute),
      );

      if (!hasBasicDependentValidationsPassed) {
        return false;
      }

      const hasAnyRowFieldsChanged = enabledLeaveAttributes?.some((selectedAttribute) =>
        hasValidChangesInRow(selectedLeavePolicyDetails, selectedAttribute),
      );

      return hasAnyRowFieldsChanged;
    },
    [typedFormDetails, leaveFormState],
  );

  const onUpdate = () => {
    updateLeavePolicy.mutate();
  };

  const onCreate = () => {
    createLeavePolicy.mutate();
  };

  const headerTitle = useMemo(() => {
    if (actionMode === "duplicate") {
      return `Duplicate ${selectedRow?.name}`;
    }
    if (isEdit) {
      return translations.editLeavePolicy;
    }
    return translations.addLeavePolicy;
  }, [actionMode, isEdit, selectedRow]);

  return (
    <Box display="flex" flexDirection="column" gap={1} position="relative" overflow="hidden">
      <Box display="flex" flexDirection="column" sx={{ width: "100%" }} gap={2} margin={1}>
        <ContentHeader showBackButton goBack={onBackClick} title={headerTitle} />
        <Divider />
      </Box>
      <Grid2 container spacing={2}>
        <Grid2 size={3}>
          <CustomTextField
            title={translations.name}
            id="name"
            size="small"
            fullWidth
            value={typedFormDetails?.name}
            onChange={handleChange}
            placeholder={translations.name}
            required
            error={!!typedFormErrors.name}
            helperText={!!typedFormErrors?.name && typedFormErrors?.name}
          />
        </Grid2>
        <Grid2 size={3}>
          <InputLabel sx={{ marginBottom: "8px", fontSize: "14px" }} htmlFor="employeeType" required>
            {translations.employeeType}
          </InputLabel>
          <Tooltip title={typedFormDetails?.employeeType?.toString()} placement="top">
            <Select
              id="employeeType"
              size="small"
              fullWidth
              displayEmpty
              disabled={!employeeTypes.isFetched}
              multiple
              startAdornment={!employeeTypes.isFetched && <CircularProgress />}
              value={typedFormDetails?.employeeType}
              renderValue={(selected) => {
                if (!selected?.length) {
                  return <Typography color="gray">Select Employee Type</Typography>;
                }
                return selected.join(", ");
              }}
              onChange={handleCheckboxChange}
              required
            >
              {employeeTypes?.data?.map((employeeType) => (
                <MenuItem key={employeeType as Key} value={employeeType as string}>
                  <Checkbox checked={typedFormDetails?.employeeType.includes(employeeType as string)}></Checkbox>
                  <ListItemText primary={employeeType as string} />
                </MenuItem>
              ))}
            </Select>
          </Tooltip>
        </Grid2>

        <Grid2 size={3}>
          <CustomDateField
            title={translations.startDate}
            required
            minDate={subYears(startOfYear(new Date()), 1)}
            maxDate={addYears(endOfYear(new Date()), 1)}
            format="MMM yyyy"
            views={["month", "year"]}
            slotProps={{
              textField: {
                id: "startDate",
                size: "small",
                fullWidth: true,
                error: !!typedFormErrors.startDate,
                helperText: !!typedFormErrors?.startDate && typedFormErrors?.startDate,
                placeholder: "Start Month",
              },
            }}
            value={typedFormDetails?.startDate as unknown as Date}
            onChange={(date: Date | null) => setFormDetail("startDate", startOfMonth(date as Date))}
          />
        </Grid2>
        <Grid2 size={3}>
          <CustomDateField
            title={translations.endDate}
            minDate={typedFormDetails?.startDate as unknown as Date}
            required
            format="MMM yyyy"
            views={["month", "year"]}
            disabled={!typedFormDetails?.startDate}
            maxDate={addYears(endOfYear(typedFormDetails?.startDate), 1)}
            slotProps={{
              textField: {
                id: "endDate",
                size: "small",
                fullWidth: true,
                error: !!typedFormErrors.endDate,
                helperText: !!typedFormErrors?.endDate && typedFormErrors?.endDate,
                placeholder: "End Month",
              },
            }}
            value={typedFormDetails?.endDate as unknown as Date}
            onChange={(date: Date | null) => setFormDetail("endDate", endOfMonth(date as Date))}
          />
        </Grid2>
      </Grid2>
      <Box
        display="flex"
        flexDirection="column"
        gap={2}
        sx={{
          maxHeight: window.innerHeight - 400,
          overflow: "scroll",
        }}
      >
        <AddEditLeavePolicyListItemTable
          dropdownOptions={{
            accrualFrequency: proRataFrequencies?.data || [],
            allocationMethod: allocationMethods?.data || [],
          }}
          handleFormChange={(key, value, index) => handleFormChange(key, value, index)}
          leaveTypes={leaveFormState}
        />
      </Box>
      <Box
        position="fixed"
        bottom={0}
        right={0}
        padding={4}
        display="flex"
        justifyContent="flex-end"
        zIndex={2}
        width="object-fit"
      >
        <Button
          disabled={isEdit && selectedRow ? !isUpdateEnabled(selectedRow) : !isCreateEnabled()}
          variant="contained"
          onClick={() => (selectedRow && isEdit ? onUpdate() : onCreate())}
        >
          {selectedRow && isEdit ? "Save" : "Create"}
        </Button>
      </Box>
    </Box>
  );
};

export default AddEditLeavePolicies;
