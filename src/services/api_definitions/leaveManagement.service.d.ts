export type HolidayResponse = {
  name: string;
  type: string;
  date: string;
};

export interface OrganisationHolidays {
  name: string;
  location: string;
  country: string;
  year: string;
  mandatory_holidays: HolidayResponse[];
  restricted_holidays: HolidayResponse[];
}

export type CreateOrganisationHoliday = {
  country: string;
  year: string;
  location: string;
  mandatory_holidays: HolidayResponse[];
  restricted_holidays: HolidayResponse[];
};

export type UpdateOrganisationHoliday = CreateOrganisationHoliday;

export type CreateHoliday = {
  name: string;
  date: string;
  country: string;
};

export type DeleteHoliday = {
  country: string;
  location: string;
  year: string;
};

export interface LeaveDetails {
  default_leaves: Leaf[];
  custom_leaves: Leaf[];
}

type Leaf = {
  description: string;
  paid: boolean;
  approver: string;
  type: string;
  short_name: string;
};

export interface LeaveEntities {
  description: string;
  paid: boolean;
  approver: string;
  type: string;
  short_name: string;
}

export type CreateLeaveType = {
  type: string;
  description: string;
  isPaid: boolean;
  approver: string;
  short_name: string;
};

export type UpdateLeaveType = CreateLeaveType & {
  new_type: string;
};

export type DeleteLeaveType = CreateLeaveType;

export interface LeavePolicyDetails {
  name: string;
  startDate: string;
  endDate: string;
  employeeTypes: string[];
  leaveAttributes: LeaveAttribute[];
  autoApproveDays?: number | null;
}

export interface LeaveAttribute {
  leaveType: string | null;
  days: number | null;
  carryForwardLimit: number | null;
  accrualFrequency: string | null;
  minimumAllowed: number | null;
  maximumAllowed: number | null;
  enabled: boolean;
  oneTime: boolean;
  carryForwardAllowed: boolean;
  halfDayAllowed: boolean;
  weekendIncluded: boolean;
  holidayIncluded: boolean;
  allowedOnProbation: boolean;
  allowedOnNotice: boolean;
  allocationMethod: string | null;
  allocationMethod: "Pro-Rated Accrued" | "In Advance" | "Fixed One-Time";
  autoApproveDays?: number | null;
}

export type UpdateLeavePolicyDetails = LeavePolicyDetails & {
  newName: string;
};
export type DeleteLeavePolicyDetails = LeavePolicyDetails;
