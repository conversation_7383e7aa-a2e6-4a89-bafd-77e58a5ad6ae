import { ArrowRightAltSharp, CalendarToday, InfoOutlined, Schedule } from "@mui/icons-material";
import { Box, Button, Card, Chip, IconButton, Paper, Tooltip, Typography } from "@mui/material";
// GoalCard.jsx
import React from "react";
import {
  PerformanceCardIconV1,
  PerformanceCardIconV2,
  PerformanceCardIconV3,
  PerformanceCardIconV4,
} from "src/assets/icons.svg";
import { formatDateToDayMonthYear, formatPeriodRange } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

interface GoalCardProps {
  quarter: string;
  status: string;
  isAddGoal: boolean;
  iconIndex: number;
  onAddGoalClick?: () => void;
  onViewAddedGoalClick?: () => void;
  buttonTitle: string;
  disableIcon?: boolean;
  reason?: string;
  isDisabled?: boolean;
  start_date?: string;
  end_date?: string;
  dueDate?: string;
}

const CardIconConfig = [PerformanceCardIconV1, PerformanceCardIconV2, PerformanceCardIconV3, PerformanceCardIconV4];

// Status Badge Component
const StatusBadge: React.FC<{ status: string; reason?: string }> = ({ status, reason }) => {
  const statusColor = getStatusColors(status);

  return (
    <Box sx={{ position: "absolute", top: 16, right: 16, zIndex: 2 }}>
      <Chip
        label={status}
        size="small"
        sx={{
          backgroundColor: statusColor,
          color: "white",
          fontWeight: 600,
          fontSize: "0.75rem",
          height: "24px",
          "& .MuiChip-label": {
            px: 1.5,
          },
        }}
      />
      {status === "Sent Back" && reason && (
        <Tooltip title={reason} placement="top">
          <IconButton
            size="small"
            sx={{
              position: "absolute",
              top: -4,
              right: -4,
              backgroundColor: "white",
              boxShadow: 1,
              width: 20,
              height: 20,
              "&:hover": {
                backgroundColor: "#f5f5f5",
              },
            }}
          >
            <InfoOutlined sx={{ fontSize: 12, color: "#FF4E4E" }} />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};

const GoalCard: React.FC<GoalCardProps> = ({
  quarter,
  status,
  isAddGoal,
  iconIndex,
  onAddGoalClick,
  onViewAddedGoalClick,
  buttonTitle = "",
  disableIcon = false,
  reason,
  isDisabled = false,
  start_date,
  end_date,
  dueDate,
}) => {
  const Icon = CardIconConfig[iconIndex];

  // Format period range for display
  const periodText = start_date && end_date ? formatPeriodRange(start_date, end_date) : null;

  // Format due date for display
  const dueDateText = dueDate ? formatDateToDayMonthYear(dueDate) : null;

  return (
    <Tooltip placement="top" title={isDisabled ? "Goal setting period is over for this performance cycle" : ""}>
      <Card
        component={Paper}
        elevation={2}
        sx={{
          width: "100%",
          minHeight: 280,
          borderRadius: "12px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: "20px",
          marginBottom: "20px",
          position: "relative",
          opacity: isDisabled ? 0.5 : 1,
          backgroundColor: "white",
          border: "1px solid #f0f0f0",
          "&:hover": {
            boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
            transform: "translateY(-2px)",
          },
          transition: "all 0.3s ease-in-out",
        }}
      >
        {/* Status Badge */}
        <StatusBadge status={status} reason={reason} />

        {/* Main Content */}
        <Box display="flex" flexDirection="column" gap={2} sx={{ paddingRight: "60px" }}>
          {/* Title */}
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              fontSize: "1.1rem",
              color: "#1a1a1a",
              lineHeight: 1.3,
            }}
          >
            {quarter}
          </Typography>

          {/* Cycle Information */}
          {periodText && (
            <Box display="flex" alignItems="center" gap={1}>
              <CalendarToday sx={{ fontSize: 16, color: "#666" }} />
              <Typography variant="body2" sx={{ fontSize: "0.875rem", color: "#666" }}>
                Cycle: {periodText}
              </Typography>
            </Box>
          )}

          {/* Due Date Information */}
          {dueDateText && (
            <Box display="flex" alignItems="center" gap={1}>
              <Schedule sx={{ fontSize: 16, color: "#666" }} />
              <Typography variant="body2" sx={{ fontSize: "0.875rem", color: "#666" }}>
                Due Date: {dueDateText}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Action Button Area */}
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-end"
          width="100%"
          sx={{ position: "relative", mt: 2 }}
        >
          {!isAddGoal ? (
            !disableIcon && (
              <IconButton
                disabled={isDisabled}
                onClick={onViewAddedGoalClick}
                sx={{
                  backgroundColor: "#E6F2F1",
                  color: "#00796B",
                  "&:hover": {
                    backgroundColor: "#B2DFDB",
                  },
                  "&:disabled": {
                    backgroundColor: "#f5f5f5",
                    color: "#ccc",
                  },
                }}
              >
                <ArrowRightAltSharp />
              </IconButton>
            )
          ) : (
            <Button
              disabled={isDisabled}
              variant="contained"
              onClick={onAddGoalClick}
              sx={{
                backgroundColor: "#00796B",
                color: "white",
                fontWeight: 600,
                textTransform: "none",
                borderRadius: "8px",
                px: 3,
                py: 1,
                "&:hover": {
                  backgroundColor: "#00695C",
                },
                "&:disabled": {
                  backgroundColor: "#ccc",
                  color: "#999",
                },
              }}
            >
              {buttonTitle}
            </Button>
          )}

          {/* Decorative Icon */}
          <Box
            position="absolute"
            right={0}
            bottom={-8}
            sx={{
              zIndex: 1,
              opacity: 0.8,
            }}
          >
            <Icon />
          </Box>
        </Box>
      </Card>
    </Tooltip>
  );
};

export default GoalCard;
